import BaseService from './BaseService'
import User from 'App/Models/User'

export default class UserService extends BaseService {
  constructor() {
    super(User)
  }

  public async findByEmail(email: string) {
    return await User.findBy('email', email)
  }

  public async createUser(data: {
    email: string
    password: string
    username: string
  }) {
    return await this.create(data)
  }

  public async updateProfile(userId: number, data: {
    email?: string
    username?: string
  }) {
    return await this.update(userId, data)
  }
}