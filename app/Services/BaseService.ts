export default class BaseService {
  protected model: any

  constructor(model: any) {
    this.model = model
  }

  public async getAll() {
    return await this.model.all()
  }

  public async getById(id: number) {
    return await this.model.findOrFail(id)
  }

  public async create(data: any) {
    return await this.model.create(data)
  }

  public async update(id: number, data: any) {
    const instance = await this.getById(id)
    return await instance.merge(data).save()
  }

  public async delete(id: number) {
    const instance = await this.getById(id)
    return await instance.delete()
  }
}