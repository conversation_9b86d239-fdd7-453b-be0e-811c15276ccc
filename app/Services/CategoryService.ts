import BaseService from './BaseService'
import Category from 'App/Models/Category'

export default class CategoryService extends BaseService {
  constructor() {
    super(Category)
  }

  public async getAllCategories() {
    return await Category.query().preload('posts')
  }

  public async getBySlug(slug: string) {
    return await Category.findByOrFail('slug', slug)
  }

  public async createCategory(data: {
    name: string
    slug: string
    description?: string
  }) {
    return await this.create(data)
  }

  public async updateCategory(id: number, data: {
    name?: string
    slug?: string
    description?: string
  }) {
    return await this.update(id, data)
  }
}