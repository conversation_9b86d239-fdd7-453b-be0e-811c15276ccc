import BaseService from './BaseService'
import Post from 'App/Models/Post'

export default class PostService extends BaseService {
  constructor() {
    super(Post)
  }

  public async getAllPosts(page: number = 1, limit: number = 10) {
    return await Post.query()
      .preload('author')
      .preload('category')
      .preload('comments')
      .paginate(page, limit)
  }

  public async getBySlug(slug: string) {
    return await Post.query()
      .where('slug', slug)
      .preload('author')
      .preload('category')
      .preload('comments')
      .firstOrFail()
  }

  public async createPost(data: {
    title: string
    content: string
    userId: number
    categoryId: number
    slug: string
    isPublished?: boolean
  }) {
    return await this.create(data)
  }

  public async updatePost(id: number, data: {
    title?: string
    content?: string
    categoryId?: number
    slug?: string
    isPublished?: boolean
  }) {
    return await this.update(id, data)
  }

  public async getByCategory(categoryId: number, page: number = 1, limit: number = 10) {
    return await Post.query()
      .where('category_id', categoryId)
      .preload('author')
      .preload('category')
      .paginate(page, limit)
  }

  public async getByUser(userId: number, page: number = 1, limit: number = 10) {
    return await Post.query()
      .where('user_id', userId)
      .preload('category')
      .paginate(page, limit)
  }
}