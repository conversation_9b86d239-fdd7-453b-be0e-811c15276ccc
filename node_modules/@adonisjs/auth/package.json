{"name": "@adonisjs/auth", "description": "Official authentication provider for Adonis framework", "version": "9.5.0", "engines": {"node": ">=18.16.0"}, "main": "build/index.js", "type": "module", "files": ["build", "!build/bin", "!build/tests", "!build/factories"], "exports": {".": "./build/index.js", "./types": "./build/src/types.js", "./auth_provider": "./build/providers/auth_provider.js", "./mixins/lucid": "./build/src/mixins/lucid.js", "./plugins/api_client": "./build/src/plugins/japa/api_client.js", "./plugins/browser_client": "./build/src/plugins/japa/browser_client.js", "./services/main": "./build/services/auth.js", "./initialize_auth_middleware": "./build/src/middleware/initialize_auth_middleware.js", "./access_tokens": "./build/modules/access_tokens_guard/main.js", "./types/access_tokens": "./build/modules/access_tokens_guard/types.js", "./session": "./build/modules/session_guard/main.js", "./types/session": "./build/modules/session_guard/types.js", "./basic_auth": "./build/modules/basic_auth_guard/main.js", "./types/basic_auth": "./build/modules/basic_auth_guard/types.js"}, "scripts": {"pretest": "npm run lint", "test": "npm run test:mysql && npm run test:pg && npm run test:mssql && c8 npm run test:sqlite", "test:sqlite": "npm run quick:test", "test:mysql": "cross-env DB=mysql npm run quick:test", "test:mssql": "cross-env DB=mssql npm run quick:test", "test:pg": "cross-env DB=pg npm run quick:test", "lint": "eslint .", "format": "prettier --write .", "typecheck": "tsc --noEmit", "clean": "del-cli build", "copy:templates": "copyfiles \"stubs/**/*.stub\" --up=\"1\" build", "precompile": "npm run lint && npm run clean", "compile": "tsup-node && tsc --emitDeclarationOnly --declaration", "postcompile": "npm run copy:templates", "build": "npm run compile", "prepublishOnly": "npm run build", "release": "release-it", "version": "npm run build", "quick:test": "cross-env NODE_DEBUG=\"adonisjs:auth:*\" node --enable-source-maps --import=ts-node-maintained/register/esm ./bin/test.js"}, "devDependencies": {"@adonisjs/assembler": "^7.8.2", "@adonisjs/core": "^6.19.0", "@adonisjs/eslint-config": "^2.1.2", "@adonisjs/hash": "^9.1.1", "@adonisjs/i18n": "^2.2.2", "@adonisjs/lucid": "^21.8.0", "@adonisjs/prettier-config": "^1.4.5", "@adonisjs/session": "^7.5.1", "@adonisjs/tsconfig": "^1.4.1", "@japa/api-client": "^3.1.0", "@japa/assert": "^4.1.1", "@japa/browser-client": "^2.1.1", "@japa/expect-type": "^2.0.3", "@japa/file-system": "^2.3.2", "@japa/plugin-adonisjs": "^4.0.0", "@japa/runner": "^4.4.0", "@japa/snapshot": "^2.0.9", "@release-it/conventional-changelog": "^10.0.1", "@swc/core": "1.13.5", "@types/basic-auth": "^1.1.8", "@types/luxon": "^3.7.1", "@types/node": "^24.5.2", "@types/set-cookie-parser": "^2.4.10", "@types/sinon": "^17.0.4", "c8": "^10.1.3", "convert-hrtime": "^5.0.0", "copyfiles": "^2.4.1", "cross-env": "^10.0.0", "del-cli": "^7.0.0", "dotenv": "^17.2.2", "eslint": "^9.36.0", "luxon": "^3.7.2", "mysql2": "^3.15.0", "nock": "^14.0.10", "pg": "^8.16.3", "playwright": "^1.55.0", "prettier": "^3.6.2", "release-it": "^19.0.5", "set-cookie-parser": "^2.7.1", "sinon": "^21.0.0", "sqlite3": "^5.1.7", "tedious": "^18.6.1", "timekeeper": "^2.3.1", "ts-node-maintained": "^10.9.6", "tsup": "^8.5.0", "typescript": "^5.9.2"}, "dependencies": {"@adonisjs/presets": "^2.6.4", "@poppinss/utils": "^6.10.1", "basic-auth": "^2.0.1"}, "peerDependencies": {"@adonisjs/core": "^6.11.0", "@adonisjs/lucid": "^20.0.0 || ^21.0.1", "@adonisjs/session": "^7.4.1", "@japa/api-client": "^2.0.3 || ^3.0.0", "@japa/browser-client": "^2.0.3", "@japa/plugin-adonisjs": "^3.0.1 || ^4.0.0"}, "peerDependenciesMeta": {"@adonisjs/lucid": {"optional": true}, "@adonisjs/session": {"optional": true}, "@japa/api-client": {"optional": true}, "@japa/browser-client": {"optional": true}, "@japa/plugin-adonisjs": {"optional": true}}, "homepage": "https://github.com/adonisjs/auth#readme", "repository": {"type": "git", "url": "git+https://github.com/adonisjs/auth.git"}, "bugs": {"url": "https://github.com/adonisjs/auth/issues"}, "keywords": ["ad<PERSON><PERSON><PERSON><PERSON>", "authentication", "auth"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "publishConfig": {"access": "public", "provenance": true}, "release-it": {"git": {"requireCleanWorkingDir": true, "requireUpstream": true, "commitMessage": "chore(release): ${version}", "tagAnnotation": "v${version}", "push": true, "tagName": "v${version}"}, "github": {"release": true}, "npm": {"publish": true, "skipChecks": true}, "plugins": {"@release-it/conventional-changelog": {"preset": {"name": "angular"}}}}, "tsup": {"entry": ["./index.ts", "./src/types.ts", "./providers/auth_provider.ts", "./src/plugins/japa/api_client.ts", "./src/plugins/japa/browser_client.ts", "./services/auth.ts", "./src/mixins/lucid.ts", "./src/middleware/initialize_auth_middleware.ts", "./modules/access_tokens_guard/main.ts", "./modules/access_tokens_guard/types.ts", "./modules/session_guard/main.ts", "./modules/session_guard/types.ts", "./modules/basic_auth_guard/main.ts", "./modules/basic_auth_guard/types.ts"], "outDir": "./build", "clean": true, "format": "esm", "dts": false, "sourcemap": false, "target": "esnext"}, "c8": {"reporter": ["text", "html"], "exclude": ["tests/**", "backup/**", "factories/**"]}, "prettier": "@adonisjs/prettier-config"}